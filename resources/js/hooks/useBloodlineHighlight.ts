import { useBloodlineStore } from '@/store/bloodline';
import { useTreeStore } from '@/store/tree';
import { useCallback } from 'react';

export const useBloodlineHighlight = () => {
  const nodes = useTreeStore((state) => state.nodes);
  const { selectNode, clearHighlights, isNodeHighlighted } = useBloodlineStore();

  const handleNodeClick = useCallback(
    (nodeId: string | null) => {
      selectNode(nodeId, nodes);
    },
    [selectNode, nodes]
  );

  return {
    handleNodeClick,
    clearHighlights,
    isNodeHighlighted,
  };
};
