import { AppNode } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { create } from 'zustand';

export type BloodlineState = {
  selectedNodeId: string | null;
  highlightedNodeIds: Set<string>;
};

export type BloodlineActions = {
  selectNode: (nodeId: string | null, nodes: AppNode[]) => void;
  clearHighlights: () => void;
  isNodeHighlighted: (nodeId: string) => boolean;
};

export type BloodlineStore = BloodlineState & BloodlineActions;

const calculateBloodline = (selectedNodeId: string, nodes: AppNode[]): string[] => {
  if (!selectedNodeId || nodes.length === 0) {
    return [];
  }

  // Create a map for efficient lookups
  const nodeMap = new Map<string, NodeModel>();
  const childrenMap = new Map<string, string[]>();

  // Build maps from the nodes data
  nodes.forEach((node) => {
    const nodeModel = node.data.nodeModel;
    nodeMap.set(node.id, nodeModel);

    // Build parent-to-children mapping
    if (nodeModel.parent_id !== null && nodeModel.parent_id !== undefined) {
      const parentId = nodeModel.parent_id.toString();
      if (!childrenMap.has(parentId)) {
        childrenMap.set(parentId, []);
      }
      childrenMap.get(parentId)!.push(node.id);
    }
  });

  const selectedNode = nodeMap.get(selectedNodeId);
  if (!selectedNode) {
    return [];
  }

  // Calculate ancestors (going up the tree)
  const ancestors: string[] = [];
  let currentNodeId: string | null = selectedNode.parent_id?.toString() || null;

  while (currentNodeId && nodeMap.has(currentNodeId)) {
    ancestors.push(currentNodeId);
    const currentNode = nodeMap.get(currentNodeId)!;
    currentNodeId = currentNode.parent_id?.toString() || null;
  }

  // Calculate descendants (going down the tree)
  const descendants: string[] = [];
  const visitDescendants = (nodeId: string) => {
    const children = childrenMap.get(nodeId) || [];
    children.forEach((childId) => {
      descendants.push(childId);
      visitDescendants(childId);
    });
  };

  visitDescendants(selectedNodeId);

  // Combine all bloodline members (ancestors + selected + descendants)
  return [...ancestors, selectedNodeId, ...descendants];
};

export const useBloodlineStore = create<BloodlineStore>((set, get) => ({
  selectedNodeId: null,
  highlightedNodeIds: new Set<string>(),

  selectNode: (nodeId, nodes) => {
    if (!nodeId) {
      set({
        selectedNodeId: null,
        highlightedNodeIds: new Set<string>(),
      });
      return;
    }

    // Calculate bloodline for the selected node
    const bloodline = calculateBloodline(nodeId, nodes);

    // Update store with selected node and highlighted bloodline
    set({
      selectedNodeId: nodeId,
      highlightedNodeIds: new Set(bloodline),
    });
  },

  clearHighlights: () =>
    set({
      selectedNodeId: null,
      highlightedNodeIds: new Set<string>(),
    }),

  isNodeHighlighted: (nodeId) => {
    const { highlightedNodeIds } = get();
    return highlightedNodeIds.has(nodeId);
  },
}));
